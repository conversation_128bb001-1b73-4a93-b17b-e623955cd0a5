#include <Arduino.h>

#include <SoftwareSerial.h>

// Definir pines para botones
#define BTN_HUMIDITY 2    // Botón para solicitar humedad
#define BTN_TEMPERATURE 3 // Botón para solicitar temperatura
#define BTN_CO2 4         // Botón para solicitar CO2

// Definir pines para comunicación serial
#define RX_PIN 10
#define TX_PIN 11

// Crear objeto SoftwareSerial para comunicación con el dispositivo Modbus
SoftwareSerial modbusSerial(RX_PIN, TX_PIN); // RX, TX

// Direcciones de registros Modbus
#define REG_HUMIDITY 0x0000
#define REG_TEMPERATURE 0x0001
#define REG_CO2 0x0002

// ID del esclavo Modbus
#define SLAVE_ID 0x01

// Factores de escala (deben coincidir con los del dispositivo esclavo)
const float FACTOR_HUMIDITY = 10.0;
const float FACTOR_TEMPERATURE = 10.0;
const float FACTOR_CO2 = 100.0;

// Variables para el estado de los botones
bool lastHumidityState = HIGH;
bool lastTemperatureState = HIGH;
bool lastCO2State = HIGH;

// Variable para rastrear qué botón se presionó
uint8_t lastButtonPressed = 0; // 0=ninguno, 1=humedad, 2=temperatura, 3=CO2

// Prototipos de funciones
void sendModbusRequest(uint8_t slaveId, uint8_t functionCode, uint16_t startAddress, uint16_t numRegisters);
float readModbusResponse();
uint16_t calculateCRC(uint8_t *data, uint8_t length);

void setup() {
  // Inicializar comunicación serial para depuración
  Serial.begin(9600);
  
  // Inicializar comunicación Modbus
  modbusSerial.begin(9600);
  
  // Configurar pines de botones como entradas con pull-up
  pinMode(BTN_HUMIDITY, INPUT_PULLUP);
  pinMode(BTN_TEMPERATURE, INPUT_PULLUP);
  pinMode(BTN_CO2, INPUT_PULLUP);
  
  Serial.println(F("Sistema iniciado"));
}

void loop() {
  // Leer estado actual de los botones
  bool humidityState = digitalRead(BTN_HUMIDITY);
  bool temperatureState = digitalRead(BTN_TEMPERATURE);
  bool co2State = digitalRead(BTN_CO2);
  
  // Verificar si se presionó el botón de humedad (detectar flanco descendente)
  if (humidityState == LOW && lastHumidityState == HIGH) {
    Serial.println(F("\nSolicitando humedad..."));
    lastButtonPressed = 1;
    sendModbusRequest(SLAVE_ID, 0x03, REG_HUMIDITY, 1);
    float humidity = readModbusResponse();
    if (humidity != -1) {
      Serial.print(F("Humedad: "));
      Serial.print(humidity);
      Serial.println(F(" %"));
    }
    delay(200); // Debounce
  }
  
  // Verificar si se presionó el botón de temperatura
  if (temperatureState == LOW && lastTemperatureState == HIGH) {
    Serial.println(F("\nSolicitando temperatura..."));
    lastButtonPressed = 2;
    sendModbusRequest(SLAVE_ID, 0x03, REG_TEMPERATURE, 1);
    float temperature = readModbusResponse();
    if (temperature != -1) {
      Serial.print(F("Temperatura: "));
      Serial.print(temperature);
      Serial.println(F(" °C"));
    }
    delay(200); // Debounce
  }
  
  // Verificar si se presionó el botón de CO2
  if (co2State == LOW && lastCO2State == HIGH) {
    Serial.println(F("\nSolicitando CO2..."));
    lastButtonPressed = 3;
    sendModbusRequest(SLAVE_ID, 0x03, REG_CO2, 1);
    float co2 = readModbusResponse();
    if (co2 != -1) {
      Serial.print(F("CO2: "));
      Serial.print(co2);
      Serial.println(F(" ppm"));
    }
    delay(200); // Debounce
  }
  
  // Actualizar estados anteriores de los botones
  lastHumidityState = humidityState;
  lastTemperatureState = temperatureState;
  lastCO2State = co2State;
  
  delay(50); // Pequeña pausa para estabilidad
}

// Función para leer y procesar la respuesta Modbus
float readModbusResponse() {
  // Esperar respuesta (timeout de 1 segundo)
  unsigned long startTime = millis();
  while (modbusSerial.available() < 7) { // Mínimo 7 bytes para una respuesta válida
    if (millis() - startTime > 1000) {
      Serial.println(F("Error: Timeout esperando respuesta"));
      return -1;
    }
    delay(10);
  }
  
  // Leer respuesta
  uint8_t response[25]; // Buffer suficientemente grande
  uint8_t byteCount = 0;
  
  // Leer todos los bytes disponibles
  delay(100); // Pequeña pausa para asegurar que todos los bytes lleguen
  while (modbusSerial.available()) {
    response[byteCount++] = modbusSerial.read();
    if (byteCount >= 25) break; // Evitar desbordamiento del buffer
  }
  
  // Mostrar respuesta completa
  Serial.print(F("Respuesta recibida: "));
  for (int i = 0; i < byteCount; i++) {
    if (response[i] < 0x10) Serial.print(F("0"));
    Serial.print(response[i], HEX);
    Serial.print(F(" "));
  }
  Serial.println();
  
  // Verificar si es una respuesta de error
  if ((response[1] & 0x80) != 0) {
    Serial.print(F("Error Modbus: 0x"));
    Serial.println(response[2], HEX);
    return -1;
  }
  
  // Verificar que la respuesta tenga el formato correcto
  if (byteCount < 7 || response[0] != SLAVE_ID || response[1] != 0x03) {
    Serial.println(F("Error: Respuesta inválida"));
    return -1;
  }
  
  // Verificar CRC
  uint16_t receivedCRC = (response[byteCount - 1] << 8) | response[byteCount - 2];
  uint16_t calculatedCRC = calculateCRC(response, byteCount - 2);
  
  if (receivedCRC != calculatedCRC) {
    Serial.print(F("Error: CRC inválido. Recibido: 0x"));
    Serial.print(receivedCRC, HEX);
    Serial.print(F(", Calculado: 0x"));
    Serial.println(calculatedCRC, HEX);
    return -1;
  }
  
  // Extraer valor (los datos están en los bytes 3 y 4 para un solo registro)
  if (response[2] == 2) { // Verificar que haya 2 bytes de datos
    // El valor está en formato big-endian (byte alto primero)
    uint16_t rawValue = (response[3] << 8) | response[4];
    
    Serial.print(F("Valor raw: 0x"));
    Serial.print(rawValue, HEX);
    Serial.print(F(" ("));
    Serial.print(rawValue);
    Serial.println(F(")"));
    
    float value = 0;
    
    // Determinar qué tipo de dato estamos recibiendo basado en lastButtonPressed
    switch (lastButtonPressed) {
      case 1: // Humedad
        value = (float)rawValue / FACTOR_HUMIDITY;
        Serial.print(F("Aplicando factor de humedad: "));
        Serial.println(FACTOR_HUMIDITY);
        break;
      case 2: // Temperatura
        value = (float)rawValue / FACTOR_TEMPERATURE;
        Serial.print(F("Aplicando factor de temperatura: "));
        Serial.println(FACTOR_TEMPERATURE);
        break;
      case 3: // CO2
        value = (float)rawValue / FACTOR_CO2;
        Serial.print(F("Aplicando factor de CO2: "));
        Serial.println(FACTOR_CO2);
        break;
      default:
        Serial.println(F("Error: No se pudo determinar qué botón se presionó"));
        return -1;
    }
    
    return value;
  }
  
  Serial.println(F("Error: Formato de datos inesperado"));
  return -1;
}

// Función para calcular el CRC-16 Modbus
uint16_t calculateCRC(uint8_t *data, uint8_t length) {
  uint16_t crc = 0xFFFF;
  for (uint8_t i = 0; i < length; i++) {
    crc ^= data[i];
    for (uint8_t j = 0; j < 8; j++) {
      if (crc & 0x0001) {
        crc = (crc >> 1) ^ 0xA001;
      } else {
        crc = crc >> 1;
      }
    }
  }
  return crc;
}

void sendModbusRequest(uint8_t slaveId, uint8_t functionCode, uint16_t startAddress, uint16_t numRegisters) {
  uint8_t request[8];
  
  // Construir trama Modbus
  request[0] = slaveId;          // ID del esclavo
  request[1] = functionCode;     // Código de función (0x03 = Read Holding Registers)
  request[2] = startAddress >> 8;    // Dirección de inicio (byte alto)
  request[3] = startAddress & 0xFF;  // Dirección de inicio (byte bajo)
  request[4] = numRegisters >> 8;    // Número de registros (byte alto)
  request[5] = numRegisters & 0xFF;  // Número de registros (byte bajo)
  
  // Calcular CRC
  uint16_t crc = calculateCRC(request, 6);
  request[6] = crc & 0xFF;      // CRC (byte bajo)
  request[7] = crc >> 8;        // CRC (byte alto)
  
  // Limpiar buffer de recepción
  while (modbusSerial.available()) modbusSerial.read();
  
  // Enviar trama
  modbusSerial.write(request, 8);
  
  // Mostrar trama enviada
  Serial.print(F("Trama enviada: "));
  for (int i = 0; i < 8; i++) {
    if (request[i] < 0x10) Serial.print(F("0"));
    Serial.print(request[i], HEX);
    Serial.print(F(" "));
  }
  Serial.println();
}
